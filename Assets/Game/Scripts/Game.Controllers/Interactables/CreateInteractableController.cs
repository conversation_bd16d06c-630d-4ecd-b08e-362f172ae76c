using System;
using Cysharp.Threading.Tasks;
using Game.Core;
using Game.Models;
using Game.Views.Consumeables;
using Game.Views.Interactables;
using Game.Views.InteractablesCore;
using Game.Views.PlayerUI;
using Game.Views.UI.Screens.Notification;
using Modules.Core;
using Modules.Network;
using Modules.UI;
using UnityEngine;
using UnityEngine.XR.Interaction.Toolkit;
using VContainer;

namespace Game.Controllers.Interactables
{
    public class CreateInteractableController : ControllerBase
    {
        private IAudioClient audioClient;
        private INetworkClient networkClient;
        private IScreenManager screenManager;
        private InteractablesManager interactablesManager;
        private SharedInteractableSettings sharedInteractableSettings;

        [Inject]
        private void Construct(
            PlayerMenu playerMenu,
            IAudioClient audioClient,
            INetworkClient networkClient,
            IScreenManager screenManager,
            InteractablesModel interactablesModel,
            InteractablesManager interactablesManager,
            SharedInteractableSettings sharedInteractableSettings)
        {
            this.audioClient = audioClient;
            this.screenManager = screenManager;
            this.networkClient = networkClient;
            this.interactablesManager = interactablesManager;
            this.sharedInteractableSettings = sharedInteractableSettings;

            playerMenu.HandsMenu.OnGrabbed.Subscribe(HandleCreateInteractable).AddTo(DisposeCancellationToken);
            playerMenu.HandsMenu.OnMoneyBagSpawned.Subscribe(HandleCreateInteractable).AddTo(DisposeCancellationToken);
            playerMenu.InteractablesMenu.OnGrabbed.Subscribe(HandleCreateInteractable).AddTo(DisposeCancellationToken);
            interactablesModel.OnInteractableCreating.Subscribe(HandleCreateInteractable).AddTo(DisposeCancellationToken);
        }

        private void HandleCreateInteractable(InteractableWidget widget)
        {
            HandleCreateInteractable(widget.InteractableCode, widget.GrabInteractor, false);
        }

        private void HandleCreateInteractable(CreateInteractableArgs args)
        {
            HandleCreateInteractable(args.code, args.interactor, args.playSound);
        }

        private void HandleCreateInteractable(string code, IXRSelectInteractor interactor, bool playSound)
        {
            if (interactor is not { hasSelection: true })
            {
                return;
            }

            var interactableCount = GetLocalPlayerInteractableCount();
            if (interactableCount >= sharedInteractableSettings.MaxInteractablesPerPlayer)
            {
                var notificationScreen = screenManager.GetScreen<NotificationScreen>();
                notificationScreen.Show("You have exceeded the maximum number of interactables", 5);
                return;
            }

            var interactable = interactablesManager.CreateActor(code);
            if (interactable != null)
            {
                if (playSound)
                {
                    audioClient.Play(AudioKeys.Goal, Vector3.zero, DisposeCancellationToken);
                }

                interactable.Grab(interactor);
            }
        }

        private int GetLocalPlayerInteractableCount()
        {
            var count = 0;

            if (networkClient.TryGetNetworkActorList<InteractableActor>(out var result))
            {
                for (var i = 0; i < result.Count; i++)
                {
                    var interactableActor = result[i];
                    if (interactableActor.HasStateAuthority && interactableActor is not ConsumableActor)
                    {
                        count++;
                    }
                }
            }

            return count;
        }
    }
}