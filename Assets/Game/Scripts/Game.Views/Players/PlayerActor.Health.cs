using System;
using System.Reactive;
using System.Reactive.Subjects;
using Fusion;
using Game.Core;
using UnityEngine;

namespace Game.Views.Players
{
    public partial class PlayerActor
    {
        private readonly ISubject<int> onMedicalConsumed = new Subject<int>();
        private readonly ISubject<Unit> onBeforeDeadLocal = new Subject<Unit>();
        private readonly ISubject<Unit> onDead = new Subject<Unit>();

        public bool IsAlive => Health > 0;
        public bool IsDead => !IsAlive;
        public IObservable<int> OnMedicalConsumed => onMedicalConsumed;
        public IObservable<Unit> OnBeforeDeadLocal => onBeforeDeadLocal;
        public IObservable<Unit> OnDead => onDead;

        [Networked] [OnChangedRender(nameof(ChangeHealth))]
        public byte Health { get; private set; }

        [Rpc(RpcSources.All, RpcTargets.StateAuthority)]
        public void ConsumeMedicalRpc(byte health)
        {
            audioClient.Play(AudioKeys.ConsumeMedical, HeadNode.position, destroyCancellationToken);
            onMedicalConsumed.OnNext(health);
        }

        public bool IsKillable(int damage)
        {
            return Health <= damage;
        }

        public void ApplyDamage(int damage)
        {
            var health = (byte)Mathf.Max(0, Health - damage);
            if (health == 0)
            {
                onBeforeDeadLocal.OnNext(Unit.Default);
            }

            Health = health;
        }

        public void ApplyHeal(int health)
        {
            Health = (byte)Mathf.Min(Health + health, playersConfig.MaxHealth);
        }

        public void RestoreHealth()
        {
            Health = (byte)playersConfig.MaxHealth;
        }

        public void SetActiveHealthSystem(bool isActive)
        {
            playerView.SetActiveHealthObject(HasStateAuthority, isActive);
        }

        private void ChangeHealth()
        {
            playerView.SetPlayerHealth(HasStateAuthority, Health, playersConfig.MaxHealth);

            if (IsDead)
            {
                onDead.OnNext(Unit.Default);
            }

            if (HasStateAuthority && IsDead)
            {
                audioClient.Play(AudioKeys.DeadPlayer, HeadNode, destroyCancellationToken);
            }
        }
    }
}