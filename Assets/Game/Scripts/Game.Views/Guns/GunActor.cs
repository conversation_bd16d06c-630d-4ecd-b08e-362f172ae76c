using Fusion;
using Game.Core;
using Game.Views.InteractablesCore;
using Game.Views.Players;
using Modules.Core;
using Modules.XR;
using UnityEngine;
using VContainer;

namespace Game.Views.Guns
{
    public abstract class GunActor<TView> : InteractableActor where TView : GunView
    {
        [SerializeField] private Transform viewParent;
        [SerializeField] protected float shootInterval = 1;
        [SerializeField] private string impactAudioKey;

        private bool CanCollide => HasStateAuthority && (IsGrabbed || (!IsGrabbed && Rigidbody.velocity.sqrMagnitude > 0.01f));

        protected PlayersModel PlayersModel { get; private set; }
        protected GunsManager GunsManager { get; private set; }
        protected GunsConfig GunsConfig { get; private set; }
        protected IAudioClient AudioClient { get; private set; }
        protected IXRInput XRInput { get; private set; }
        protected TView View { get; private set; }
        protected bool HasView => View != null;

        [Inject]
        private void Construct(GunsManager gunsManager, GunsConfig gunsConfig, IAudioClient audioClient, IXRInput xrInput, PlayersModel playersModel)
        {
            PlayersModel = playersModel;
            AudioClient = audioClient;
            GunsManager = gunsManager;
            GunsConfig = gunsConfig;
            XRInput = xrInput;
        }

        public override void Spawned()
        {
            base.Spawned();
            InitializeView();
            ChangeGrabber();
            ChangeIsAttached();
        }

        public override void Despawned(NetworkRunner runner, bool hasState)
        {
            base.Despawned(runner, hasState);
            StopDropTimer();
            UninitializeView();
        }

        protected override void ChangeGrabber()
        {
            base.ChangeGrabber();

            if (IsGrabbed)
            {
                if (HasView)
                {
                    View.SetGrabState(HasStateAuthority);
                }
            }
            else
            {
                if (HasView)
                {
                    View.SetDropState(true);
                }
            }

            if (HasStateAuthority)
            {
                UpdateDropTimer(!IsGrabbed, DropTimeout);
            }
        }

        protected override void InitializeView()
        {
            base.InitializeView();

            if (GunsManager.TryCreateView(InteractableId, viewParent, out var view) && view is TView currentView)
            {
                View = currentView;
            }
            else
            {
                GunsManager.DestroyView(view);
            }

            InteractableCode = GunsConfig.GetInteractableData(InteractableId)?.Code;
            InitializeGrabbing(View);
        }

        protected override void UninitializeView()
        {
            base.UninitializeView();
            UninitializeGrabbing();
            GunsManager?.DestroyView(View);
            View = null;
        }

        private void OnCollisionEnter(Collision collision)
        {
            if (!CanCollide)
            {
                return;
            }

            PlayImpactAudio();
        }

        private void PlayImpactAudio()
        {
            if (!HasStateAuthority || IsGrabbed)
            {
                return;
            }

            var playerList = PlayersModel.FindPlayers(transform.position);

            foreach (var player in playerList)
            {
                SendRpcSafe(() => PlayImpactAudioRpc(player.StateAuthority));
            }
        }

        [Rpc(RpcSources.StateAuthority, RpcTargets.All, Channel = RpcChannel.Unreliable)]
        private void PlayImpactAudioRpc([RpcTarget] PlayerRef player)
        {
            var key = string.IsNullOrEmpty(impactAudioKey) ? AudioKeys.ImpactDefault : impactAudioKey;
            AudioClient.Play(key, transform.position, destroyCancellationToken);
        }
    }
}