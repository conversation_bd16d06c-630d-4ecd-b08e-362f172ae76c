using System;
using Cysharp.Threading.Tasks;
using Fusion;
using Game.Core;
using Game.Views.Interactables;
using Game.Views.InteractablesCore;
using Modules.Network;
using UnityEngine;
using VContainer;

namespace Game.Views.Items
{
    public class BackpackActor : ItemActor<BackpackView>
    {
        [Header("BackpackActor")]
        [SerializeField] private int dropTimeout;

        private const int MaxInventory = 16;

        private float shakeTime;
        private float addInventoryTime;
        private float removeInventoryTime;
        private InteractablesManager interactablesManager;

        private bool HasAnyInventory => InventoryList.Count > 0;
        private bool IsFull => InventoryList.Count >= MaxInventory;
        private bool CanAddInventory => Time.time - addInventoryTime > 1 && Time.time - removeInventoryTime > 1;
        private bool CanRemoveInventory => HasAnyInventory && Time.time - removeInventoryTime > 1;

        [Networked] [OnChangedRender(nameof(ChangeIsOpen))]
        public NetworkBool IsOpen { get; private set; }

        [Networked] [OnChangedRender(nameof(ChangeIsEquipped))]
        public NetworkBool IsEquipped { get; set; }

        [Networked] [Capacity(MaxInventory)] public NetworkLinkedList<int> InventoryList { get; } = default;

        [Inject]
        private void Construct(InteractablesManager interactablesManager)
        {
            this.interactablesManager = interactablesManager;
        }

        public override void Spawned()
        {
            base.Spawned();
            ChangeIsOpen();
            ChangeIsEquipped();
        }

        public override void FixedUpdateNetwork()
        {
            base.FixedUpdateNetwork();

            if (IsGrabbed && HasAnyInventory && CanRemoveInventory)
            {
                var isLookDown = Vector3.Dot(transform.up, Vector3.down) > 0.7f;
                if (!isLookDown)
                {
                    shakeTime = -1;
                    return;
                }

                if (Mathf.Approximately(shakeTime, -1))
                {
                    shakeTime = Time.time;
                    return;
                }

                if (Time.time - shakeTime < 0.5f)
                {
                    return;
                }

                AudioClient.Play(AudioKeys.BackpackRemoveItem, transform.position, destroyCancellationToken);
                var inventoryId = InventoryList.Get(0);
                InventoryList.Remove(inventoryId);
                interactablesManager.CreateActor(inventoryId, View.PocketPose);
                removeInventoryTime = Time.time;
            }
            else
            {
                shakeTime = -1;
            }
        }

        protected override void UpdateDropTimer()
        {
            UpdateDropTimer(!IsGrabbed && !IsEquipped, dropTimeout);
        }

        protected override void OnCollisionEnter(Collision collision)
        {
            base.OnCollisionEnter(collision);
            if (HasStateAuthority
                && CanAddInventory
                && collision.gameObject.TryGetComponent(out InteractableActor interactable)
                && !interactable.IsGrabbed
                && interactable is not BackpackActor)
            {
                if (IsFull)
                {
                    AudioClient.Play(AudioKeys.BackpackFull, transform.position, destroyCancellationToken);
                }
                else
                {
                    AudioClient.Play(AudioKeys.BackpackAddItem, transform.position, destroyCancellationToken);
                    AddInventoryAsync(interactable).Forget();
                }
            }
        }

        protected override void PlayImpactAudio()
        {
            if (IsEquipped)
            {
                return;
            }

            base.PlayImpactAudio();
        }

        private void ChangeIsOpen()
        {
        }

        private void ChangeIsEquipped()
        {
            SetActiveNetworkRigidbody(!IsEquipped);
        }

        private async UniTaskVoid AddInventoryAsync(InteractableActor interactable)
        {
            if (!interactable.HasStateAuthority)
            {
                var result = await interactable.Object
                    .RequestStateAuthorityAsync(destroyCancellationToken)
                    .TimeoutWithoutException(TimeSpan.FromSeconds(1));

                if (result.IsTimeout)
                {
                    return;
                }
            }

            InventoryList.Add(interactable.InteractableId);
            Runner.Despawn(interactable.Object);
            addInventoryTime = Time.time;
        }
    }
}