using Cysharp.Threading.Tasks;
using Fusion;

namespace Modules.Network
{
    internal class MasterClientHandler : NetworkActor, IStateAuthorityChanged
    {
        private readonly IAsyncReactiveProperty<PlayerRef> masterClient = new AsyncReactiveProperty<PlayerRef>(PlayerRef.None);

        public IReadOnlyAsyncReactiveProperty<PlayerRef> MasterClient => masterClient;

        public override void Spawned()
        {
            StateAuthorityChanged();
        }

        public void StateAuthorityChanged()
        {
            if (masterClient.Value == StateAuthority)
            {
                return;
            }

            masterClient.Value = StateAuthority;
        }
    }
}